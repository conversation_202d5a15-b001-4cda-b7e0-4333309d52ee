{"artifacts": [{"path": "bin/tcpclient.exe"}, {"path": "bin/tcpclient.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "link_directories", "target_link_libraries", "target_compile_options", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 34, "parent": 0}, {"command": 1, "file": 0, "line": 54, "parent": 0}, {"command": 2, "file": 0, "line": 21, "parent": 0}, {"command": 3, "file": 0, "line": 39, "parent": 0}, {"command": 4, "file": 0, "line": 50, "parent": 0}, {"command": 5, "file": 0, "line": 17, "parent": 0}, {"command": 5, "file": 0, "line": 18, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu++17"}, {"backtrace": 5, "fragment": "-Wall"}, {"backtrace": 5, "fragment": "-Wextra"}, {"backtrace": 5, "fragment": "-pedantic"}], "includes": [{"backtrace": 6, "path": "D:/AAVM/Http_Client/include"}, {"backtrace": 7, "path": "D:/AAVM/Http_Client/include/XTcp"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0]}], "id": "tcpclient::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "bin"}], "prefix": {"path": "C:/Program Files (x86)/Http_Client"}}, "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"backtrace": 3, "fragment": "-LD:\\AAVM\\Http_Client\\lib", "role": "libraryPath"}, {"backtrace": 4, "fragment": "-lXTcp", "role": "libraries"}, {"backtrace": 4, "fragment": "-lws2_32", "role": "libraries"}, {"backtrace": 4, "fragment": "-lwsock32", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "tcpclient", "nameOnDisk": "tcpclient.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/tcpclient.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}