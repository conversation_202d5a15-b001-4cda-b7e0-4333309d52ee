{"kind": "toolchains", "toolchains": [{"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++", "D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32", "D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward", "D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include", "D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed", "D:/AAVM/software/mingw64/x86_64-w64-mingw32/include"], "linkDirectories": ["D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0", "D:/AAVM/software/mingw64/lib/gcc", "D:/AAVM/software/mingw64/x86_64-w64-mingw32/lib", "D:/AAVM/software/mingw64/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["stdc++", "mingw32", "gcc_s", "gcc", "mingwex", "kernel32", "advapi32", "shell32", "user32", "kernel32", "iconv", "mingw32", "gcc_s", "gcc", "mingwex", "kernel32"]}, "path": "D:/AAVM/software/mingw64/bin/g++.exe", "version": "15.1.0"}, "language": "CXX", "sourceFileExtensions": ["C", "M", "c++", "cc", "cpp", "cxx", "m", "mm", "mpp", "CPP", "ixx", "cppm", "ccm", "cxxm", "c++m"]}, {"compiler": {"implicit": {}, "path": "D:/AAVM/software/mingw64/bin/windres.exe"}, "language": "RC", "sourceFileExtensions": ["rc", "RC"]}], "version": {"major": 1, "minor": 0}}