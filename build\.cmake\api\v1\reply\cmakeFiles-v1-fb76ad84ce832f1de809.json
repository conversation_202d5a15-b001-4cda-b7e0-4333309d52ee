{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/CMakeFiles/4.1.0/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-Initialize.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/4.1.0/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-GNU.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/4.1.0/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-windres.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-GNU-CXX-ABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Internal/CMakeCXXLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Linker/Windows-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Linker/Windows-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Linker/Windows-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Linker/GNU.cmake"}], "kind": "cmakeFiles", "paths": {"build": "D:/AAVM/Http_Client/build", "source": "D:/AAVM/Http_Client"}, "version": {"major": 1, "minor": 1}}