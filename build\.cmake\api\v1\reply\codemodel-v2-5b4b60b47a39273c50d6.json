{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-e7962d4eef11eed58a12.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Http_Client", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "tcpclient::@6890427a1f51a3e7e1df", "jsonFile": "target-tcpclient-Debug-88c20be590e64b5004e4.json", "name": "tcpclient", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/AAVM/Http_Client/build", "source": "D:/AAVM/Http_Client"}, "version": {"major": 2, "minor": 8}}