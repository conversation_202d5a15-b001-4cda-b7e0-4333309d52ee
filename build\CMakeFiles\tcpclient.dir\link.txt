D:\C++source\Tools\Cmake\bin\cmake.exe -E rm -f CMakeFiles\tcpclient.dir/objects.a
D:\AAVM\software\mingw64\bin\ar.exe qc CMakeFiles\tcpclient.dir/objects.a @CMakeFiles\tcpclient.dir\objects1.rsp
D:\AAVM\software\mingw64\bin\g++.exe -g -Wl,--whole-archive CMakeFiles\tcpclient.dir/objects.a -Wl,--no-whole-archive -o bin\tcpclient.exe -Wl,--out-implib,lib\libtcpclient.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\tcpclient.dir\linkLibs.rsp
