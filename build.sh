#!/bin/bash

echo "========================================"
echo "Building Http_Client Project"
echo "========================================"

# 创建构建目录
mkdir -p build
cd build

# 配置项目
echo "Configuring project..."
cmake ..
if [ $? -ne 0 ]; then
    echo "Configuration failed!"
    exit 1
fi

# 编译项目
echo "Building project..."
cmake --build . --config Release
if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

echo "========================================"
echo "Build completed successfully!"
echo "Executable location: build/bin/tcpclient"
echo "========================================"

# 返回项目根目录
cd ..
