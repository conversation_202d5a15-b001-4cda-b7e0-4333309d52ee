#include "XTcp.h"
#include <iostream>
#include <string>
#include <cstring>

#ifdef WIN32
    #include <windows.h>
#else
    #include <unistd.h>
#endif

  // 直接使用你提供的类

using namespace std;

int main(int argc, char* argv[]) {
    // 默认连接地址和端口
    const char* server_ip = "127.0.0.1";
    unsigned short server_port = 8080;

    // 支持命令行参数：client [ip] [port]
    if (argc > 1) {
        server_ip = argv[1];
    }
    if (argc > 2) {
        server_port = static_cast<unsigned short>(atoi(argv[2]));
    }

    XTcp client;

    cout << "Connecting to " << server_ip << ":" << server_port << "..." << endl;

    if (!client.Connect(server_ip, server_port)) {
        cerr << "Failed to connect to server!" << endl;
        return -1;
    }

    cout << "Connected. Type your messages. Type 'quit' to exit." << endl;

    char buffer[1024];
    string input;

    while (true) {
        cout << "> ";
        getline(cin, input);

        if (input.empty()) continue;

        // 发送数据
        int sent = client.Send(input.c_str(), input.length());
        if (sent <= 0) {
            cout << "Send failed. Server may have disconnected." << endl;
            break;
        }

        // 接收服务器响应
        int recvLen = client.Recv(buffer, sizeof(buffer) - 1);
        if (recvLen <= 0) {
            cout << "Server disconnected." << endl;
            break;
        }

        cout << "Server: " << buffer << endl;

        // 如果发送了 quit，则退出
        if (input.find("quit") != string::npos) {
            cout << "Closing connection..." << endl;
            break;
        }
    }

    client.Close();
    return 0;
}