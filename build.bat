@echo off
echo ========================================
echo Building Http_Client Project
echo ========================================

:: 创建构建目录
if not exist build mkdir build
cd build

:: 配置项目
echo Configuring project...
cmake .. -G "MinGW Makefiles"
if %errorlevel% neq 0 (
    echo Configuration failed!
    pause
    exit /b 1
)

:: 编译项目
echo Building project...
cmake --build . --config Release
if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo ========================================
echo Build completed successfully!
echo Executable location: build/bin/tcpclient.exe
echo ========================================

:: 返回项目根目录
cd ..

pause
